server {
  listen 80;
  server_name _;

  root /usr/share/nginx/html;
  index index.html;
  add_header Access-Control-Allow-Origin *;

  # Include MIME types
  include /etc/nginx/mime.types;

  # Explicitly set MIME type for JavaScript files
  location ~* \.js$ {
    add_header Content-Type "application/javascript; charset=utf-8";
    expires 1y;
    add_header Cache-Control "public, immutable";
    try_files $uri =404;
  }

  # Serve static assets locally
  location /assets/ {
    gzip_static on;
    expires max;
    add_header Cache-Control public;
  }

  # Serve other static assets (excluding JS which is handled above)
  location ~* \.(css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|map)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    try_files $uri =404;
  }

  # Handle SPA routes (exclude static assets)
  location ~ ^/(dashboard|login|signup|auth/callback|confirm-email|forgot-password|reset-password|confirm-email-member-invitation|payment/finish|payment/resume|invoice|settings) {
    try_files $uri $uri/ /index.html;
  }

  # Optional: gzip static assets for better performance
  gzip on;
  gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
  gzip_min_length 256;
}
