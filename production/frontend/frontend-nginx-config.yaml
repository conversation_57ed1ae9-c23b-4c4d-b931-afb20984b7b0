apiVersion: v1
kind: ConfigMap
metadata:
  name: frontend-nginx-config
  namespace: default
data:
  default.conf: |
    server {
        listen 80;
        server_name agentq.id www.agentq.id;

        # ... (Blok 0, 1, 2, 3 - GitHub Pages - tidak berubah) ...
        location = /signin { return 301 /login; }
        location = / { return 301 /agentq/; }
        location /agentq/ {
            proxy_pass https://agentq-ai.github.io/agentq/;
            proxy_set_header Host               agentq-ai.github.io;
            # ... (lainnya)
        }
        location /docs/ {
            rewrite ^/docs/(.*)$ /agentq/docs/$1 break;
            proxy_pass https://agentq-ai.github.io;
            proxy_set_header Host               agentq-ai.github.io;
            # ... (lainnya)
        }

        ####################################################################
        # 4) PENANGANAN ASET SPA
        ####################################################################
        location ^~ /assets/ {
            proxy_pass http://frontend-agentq-svc:80;
            
            # PERUBAHAN DI SINI:
            # proxy_set_header Host $host; # <--- JANGAN GUNAKAN INI
            proxy_set_header Host frontend-agentq-svc; # <--- GUNAKAN INI
            
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto  $scheme;
            expires 30d;
            add_header Cache-Control "public, immutable";
        }

        ####################################################################
        # 5) PENANGANAN RUTE SPA (Catch-all)
        ####################################################################
        location / {
            proxy_pass http://frontend-agentq-svc:80; 
            
            # PERUBAHAN DI SINI JUGA:
            # proxy_set_header Host $host; # <--- JANGAN GUNAKAN INI
            proxy_set_header Host frontend-agentq-svc; # <--- GUNAKAN INI

            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto  $scheme;
        }

        # 9) Optional global CORS (Tidak diubah)
        add_header Access-Control-Allow-Origin *;
    }
