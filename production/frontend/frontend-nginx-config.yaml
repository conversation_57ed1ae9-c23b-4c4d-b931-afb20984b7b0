apiVersion: v1
kind: ConfigMap
metadata:
  name: frontend-nginx-config
  namespace: default
data:
  default.conf: |
    server {
        listen 80;
        server_name agentq.id www.agentq.id;

        ####################################################################
        # 0) Redirect /signin → /login
        ####################################################################
        location = /signin { return 301 /login; }

        ####################################################################
        # 1) Root  /   —> redirect ke landing page /agentq/
        ####################################################################
        location = / {
            return 301 /agentq/;
        }

        ####################################################################
        # 2) GitHub Pages landing: /agentq/
        ####################################################################
        location /agentq/ {
            proxy_pass https://agentq-ai.github.io/agentq/;
            proxy_set_header Host               agentq-ai.github.io;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto  $scheme;
            proxy_set_header Accept-Encoding    "";

            sub_filter_once  off;
            sub_filter_types text/html text/css application/javascript;
            sub_filter 'http://agentq.id/assets/' 'https://agentq.id/agentq/assets/';
            proxy_redirect https://agentq-ai.github.io/agentq/ /agentq/;
        }

        ####################################################################
        # 3) /docs/...  → redirect ke /agentq/docs/...
        ####################################################################
        location /docs/ {
            rewrite ^/docs/(.*)$ /agentq/docs/$1 break;
            proxy_pass https://agentq-ai.github.io;
            proxy_set_header Host               agentq-ai.github.io;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto  $scheme;
            proxy_set_header Accept-Encoding    "";
            proxy_redirect https://agentq-ai.github.io/agentq/ /docs/;
            expires 7d;
            add_header Cache-Control public;
        }

        ####################################################################
        # 4) CSS - handled sebelum blok /assets/
        ####################################################################
        location ~* ^/assets/.*\.css$ {
            proxy_pass http://frontend-agentq-svc:80;
            proxy_set_header Host               $host;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto  $scheme;
            expires 30d;
            add_header Cache-Control "public, immutable";
        }

        ####################################################################
        # 5) JS - handled sebelum blok /assets/
        ####################################################################
        location ~* ^/assets/.*\.js$ {
            proxy_pass http://frontend-agentq-svc:80;
            proxy_set_header Host               $host;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto  $scheme;
            expires 30d;
            add_header Cache-Control "public, immutable";
        }

        ####################################################################
        # 6) Other static assets (fonts, images, dll)
        ####################################################################
        location /assets/ {
            proxy_pass http://frontend-agentq-svc:80;
            proxy_set_header Host               $host;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto  $scheme;
            expires 30d;
            add_header Cache-Control "public, immutable";
        }

        ####################################################################
        # 7) SPA routes (login, dashboard, dll)
        ####################################################################
        location ~ ^/(dashboard|login|signup|auth/callback|confirm-email|forgot-password|reset-password|confirm-email-member-invitation|payment/finish|payment/resume|invoice|settings) {
            proxy_pass http://frontend-agentq-svc:80;
            proxy_set_header Host               $host;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto  $scheme;
            proxy_intercept_errors on;
            error_page 404 = @spa_fallback;
        }

        ####################################################################
        # 8) Catch-all (Vue/React SPA fallback)
        ####################################################################
        location / {
            proxy_pass http://frontend-agentq-svc:80;
            proxy_set_header Host               $host;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto  $scheme;
            proxy_intercept_errors on;
            error_page 404 = @spa_fallback;
        }

        location @spa_fallback {
            rewrite ^ / break;
            proxy_pass http://frontend-agentq-svc:80;
            proxy_set_header Host               $host;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto  $scheme;
        }

        ####################################################################
        # 9) Optional global CORS
        ####################################################################
        add_header Access-Control-Allow-Origin *;
    }