apiVersion: apps/v1
kind: Deployment
metadata:
  name: app-frontend-agentq
  namespace: default
spec:
  replicas: 2
  selector:
    matchLabels:
      app: app-frontend-agentq
  template:
    metadata:
      labels:
        app: app-frontend-agentq
    spec:
      containers:
      - name: app-frontend-agentq
        image: asia-southeast2-docker.pkg.dev/agentq-464900/agentq/app_frontend_agentq:3.1.0
        imagePullPolicy: Always
        ports:
        - containerPort: 80
        envFrom:
        - configMapRef:
            name: app-frontend-agentq-config
        - secretRef:
            name: app-frontend-agentq-secret
