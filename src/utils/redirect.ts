/**
 * Utility functions for handling redirects to the app frontend
 */

export const getAppUrl = (): string => {
  // Try to get from environment variable
  const envUrl = (import.meta as any).env.VITE_APP_URL;

  console.log('=== DEBUG: getAppUrl ===');
  console.log('import.meta.env:', (import.meta as any).env);
  console.log('VITE_APP_URL:', envUrl);

  if (envUrl && envUrl.trim()) {
    console.log('Using VITE_APP_URL from environment:', envUrl);
    return envUrl;
  }

  // Fallback to localhost
  const fallbackUrl = (import.meta as any).env.VITE_APP_URL;
  console.log('Using fallback URL:', fallbackUrl);
  return fallbackUrl;
};

export const redirectToApp = (delay: number = 100): void => {
  const appUrl = getAppUrl();
  console.log('Redirecting to app URL:', appUrl);

  setTimeout(() => {
    window.location.href = appUrl;
  }, delay);
};

export const redirectToAppWithPath = (path: string = '', delay: number = 100): void => {
  const appUrl = getAppUrl();
  const fullUrl = path ? `${appUrl}${path}` : appUrl;
  console.log('Redirecting to app URL with path:', fullUrl);

  setTimeout(() => {
    window.location.href = fullUrl;
  }, delay);
};

/**
 * Redirect to app with authentication data via URL parameters
 * This is necessary because localStorage is domain-specific
 */
export const redirectToAppWithAuth = (delay: number = 100): void => {
  const appUrl = getAppUrl();

  // Get token and user from localStorage
  const token = localStorage.getItem('token');
  const user = localStorage.getItem('user');

  if (!token || !user) {
    console.error('No token or user data found in localStorage');
    // Fallback to regular redirect
    redirectToApp(delay);
    return;
  }

  // Build URL with parameters
  const params = new URLSearchParams();
  params.append('token', token);
  params.append('user', user);

  const fullUrl = `${appUrl}/auth/callback?${params.toString()}`;
  console.log('Redirecting to app with auth:', fullUrl);

  setTimeout(() => {
    window.location.href = fullUrl;
  }, delay);
};

